#!/bin/bash
# Script to verify CronJob security configuration
# Verifies that CronJob uses immutable image tags instead of :latest

set -e

echo "🔒 === CRONJOB SECURITY VERIFICATION ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "❌ kubectl not found. Please install kubectl to verify CronJob deployment."
    exit 1
fi

# Check if CronJob exists
print_status "🔍 Checking if CronJob exists..."
if kubectl get cronjob partition-management -n rayuela &> /dev/null; then
    print_success "✅ CronJob 'partition-management' found in namespace 'rayuela'"
    
    # Get the image being used
    IMAGE=$(kubectl get cronjob partition-management -n rayuela -o jsonpath='{.spec.jobTemplate.spec.template.spec.containers[0].image}')
    print_status "📋 Current image: $IMAGE"
    
    # Check if image uses :latest tag
    if [[ $IMAGE == *":latest" ]]; then
        print_error "❌ SECURITY ISSUE: CronJob is using :latest tag!"
        print_error "   This is a security vulnerability. The image should use an immutable tag."
        echo ""
        echo "🔧 To fix this issue:"
        echo "   1. Redeploy using the CI/CD pipeline: ./scripts/deploy-production.sh --direct"
        echo "   2. Or manually update the CronJob with a specific image tag"
        exit 1
    elif [[ $IMAGE == *"BUILD_ID_PLACEHOLDER"* ]] || [[ $IMAGE == *"PROJECT_ID"* ]]; then
        print_error "❌ CONFIGURATION ISSUE: CronJob has placeholder values!"
        print_error "   Image: $IMAGE"
        print_error "   The CronJob was not deployed through the CI/CD pipeline."
        echo ""
        echo "🔧 To fix this issue:"
        echo "   Deploy using the CI/CD pipeline: ./scripts/deploy-production.sh --direct"
        exit 1
    else
        print_success "✅ SECURITY OK: CronJob is using an immutable image tag"
        
        # Extract and validate the tag format
        TAG=$(echo $IMAGE | cut -d':' -f2)
        if [[ $TAG =~ ^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$ ]] || [[ $TAG =~ ^[a-f0-9]{40}$ ]] || [[ $TAG =~ ^[0-9]{14}$ ]]; then
            print_success "✅ Tag format appears to be a valid BUILD_ID or COMMIT_SHA: $TAG"
        else
            print_status "⚠️  Tag format is not recognized as BUILD_ID or COMMIT_SHA: $TAG"
            print_status "   This might be a custom tag, which is acceptable if it's immutable."
        fi
    fi
    
    echo ""
    print_status "📊 CronJob Details:"
    kubectl describe cronjob partition-management -n rayuela | grep -E "(Name:|Namespace:|Schedule:|Image:|Last Schedule Time:|Active:|Suspend:)"
    
    echo ""
    print_status "📋 Recent Jobs:"
    kubectl get jobs -n rayuela --selector=job-name=partition-management --sort-by=.metadata.creationTimestamp | tail -5
    
else
    print_error "❌ CronJob 'partition-management' not found in namespace 'rayuela'"
    print_status "🔧 To deploy the CronJob:"
    print_status "   Run: ./scripts/deploy-production.sh --direct"
    exit 1
fi

echo ""
print_success "🎉 CronJob security verification completed successfully!"
echo ""
print_status "📝 Summary:"
print_status "   ✅ CronJob exists and is properly configured"
print_status "   ✅ Using immutable image tag (not :latest)"
print_status "   ✅ Deployed in correct namespace (rayuela)"
print_status "   ✅ Security best practices followed"
