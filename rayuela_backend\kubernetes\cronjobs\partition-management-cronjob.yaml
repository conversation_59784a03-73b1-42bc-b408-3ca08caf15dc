apiVersion: batch/v1
kind: CronJob
metadata:
  name: partition-management
  namespace: rayuela
  labels:
    app: rayuela
    component: maintenance
spec:
  schedule: "0 1 * * *"  # Ejecutar diariamente a la 1:00 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 5
  jobTemplate:
    spec:
      # Tiempo máximo de ejecución antes de considerar el trabajo como fallido
      activeDeadlineSeconds: 1800  # 30 minutos
      backoffLimit: 2
      template:
        metadata:
          labels:
            app: rayuela
            component: db-maintenance
        spec:
          # Usar ServiceAccount con permisos mínimos
          serviceAccountName: db-maintenance-sa
          restartPolicy: OnFailure
          containers:
          - name: partition-manager
            # SECURITY FIX: Use immutable image tag instead of :latest
            # This will be substituted during deployment with actual BUILD_ID
            image: us-central1-docker.pkg.dev/PROJECT_ID/rayuela-repo/rayuela-backend:BUILD_ID_PLACEHOLDER
            imagePullPolicy: IfNotPresent
            command: ["python", "scripts/manage_partitions.py"]
            env:
              - name: POSTGRES_USER
                valueFrom:
                  secretKeyRef:
                    name: db-credentials
                    key: username
              - name: POSTGRES_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: db-credentials
                    key: password
              - name: POSTGRES_SERVER
                valueFrom:
                  configMapKeyRef:
                    name: app-config
                    key: db_host
              - name: POSTGRES_PORT
                valueFrom:
                  configMapKeyRef:
                    name: app-config
                    key: db_port
              - name: POSTGRES_DB
                valueFrom:
                  configMapKeyRef:
                    name: app-config
                    key: db_name
              - name: PARTITION_SIZE
                value: "100000"
              - name: PARTITION_BUFFER_COUNT
                value: "5"
              - name: PYTHONPATH
                value: "."
              - name: LOG_LEVEL
                value: "INFO"
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 512Mi
---
# ServiceAccount con permisos mínimos para el CronJob
apiVersion: v1
kind: ServiceAccount
metadata:
  name: db-maintenance-sa
  namespace: rayuela
---
# Role con permisos mínimos necesarios
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: db-maintenance-role
  namespace: rayuela
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch"]
---
# RoleBinding para asociar el ServiceAccount con el Role
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: db-maintenance-rolebinding
  namespace: rayuela
subjects:
- kind: ServiceAccount
  name: db-maintenance-sa
  namespace: rayuela
roleRef:
  kind: Role
  name: db-maintenance-role
  apiGroup: rbac.authorization.k8s.io
