steps:
  # 1. Setup and basic validation
  - name: 'python:3.12-slim'
    id: 'setup-environment'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🚀 Setting up build environment..."
        apt-get update && apt-get install -y git curl
        echo "✅ Environment setup complete"

  # 2. Build Backend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:latest'
      - '-f'
      - 'rayuela_backend/Dockerfile'
      - 'rayuela_backend'

  # 3. Build Frontend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-frontend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:latest'
      - '-f'
      - 'rayuela_frontend/Dockerfile'
      - 'rayuela_frontend'

  # 4. Push Backend image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
    waitFor: ['build-backend']

  # 5. Push Frontend image  
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-frontend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
    waitFor: ['build-frontend']

  # 6. Deploy Backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-backend'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-backend'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--min-instances=0'
      - '--max-instances=10'
      - '--timeout=300s'
      - '--concurrency=80'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO'
      - '--set-env-vars=ALLOWED_HOSTS=["rayuela-backend-lrw7xazcbq-uc.a.run.app"]'
      - '--set-secrets=POSTGRES_PASSWORD=DB_PASSWORD:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest'
    waitFor: ['push-backend']

  # 7. Get Backend URL for Frontend
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'get-backend-url'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(gcloud run services describe rayuela-backend --region=us-central1 --format="value(status.url)")
        echo "Backend URL: $$BACKEND_URL"
        echo "$$BACKEND_URL" > /workspace/backend_url.txt
    waitFor: ['deploy-backend']

  # 8. Deploy Frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-frontend'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        echo "Deploying frontend with backend URL: $$BACKEND_URL"
        gcloud run deploy rayuela-frontend \
          --image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID \
          --region=us-central1 \
          --platform=managed \
          --allow-unauthenticated \
          --memory=1Gi \
          --cpu=1 \
          --min-instances=0 \
          --max-instances=5 \
          --timeout=300s \
          --set-env-vars=NEXT_PUBLIC_API_BASE_URL="$$BACKEND_URL",NODE_ENV=production
    waitFor: ['get-backend-url', 'push-frontend']

  # 9. Deploy CronJob with immutable image tag (SECURITY FIX)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-cronjob'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔧 Deploying partition management CronJob with immutable image tag..."

        # Create temporary CronJob file with substituted BUILD_ID
        sed "s/PROJECT_ID/$PROJECT_ID/g; s/BUILD_ID_PLACEHOLDER/$BUILD_ID/g" \
          rayuela_backend/kubernetes/cronjobs/partition-management-cronjob.yaml > /tmp/cronjob-deploy.yaml

        echo "📋 CronJob configuration:"
        cat /tmp/cronjob-deploy.yaml

        # Apply the CronJob configuration
        kubectl apply -f /tmp/cronjob-deploy.yaml

        # Verify deployment
        echo "✅ Verifying CronJob deployment..."
        kubectl get cronjobs -n rayuela
        kubectl describe cronjob partition-management -n rayuela

        echo "🎯 CronJob deployed successfully with immutable image tag: $BUILD_ID"
    waitFor: ['push-backend']

  # 10. Health check
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'health-check'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🏥 Performing health checks..."

        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        FRONTEND_URL=$$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)")

        echo "Backend URL: $$BACKEND_URL"
        echo "Frontend URL: $$FRONTEND_URL"

        # Test Backend
        echo "Testing backend health..."
        curl -f "$$BACKEND_URL/health" || echo "Backend health check failed"

        # Verify CronJob is properly configured
        echo "🔧 Verifying CronJob configuration..."
        kubectl get cronjobs -n rayuela partition-management -o jsonpath='{.spec.jobTemplate.spec.template.spec.containers[0].image}'

        echo "✅ Deployment completed successfully!"
        echo "🌐 Frontend: $$FRONTEND_URL"
        echo "🔧 Backend API: $$BACKEND_URL"
        echo "⏰ CronJob: partition-management (scheduled daily at 1:00 AM)"
    waitFor: ['deploy-frontend', 'deploy-cronjob']

# Images to store in registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:latest'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:latest'

# Timeout
timeout: '2400s'

options:
  logging: CLOUD_LOGGING_ONLY 